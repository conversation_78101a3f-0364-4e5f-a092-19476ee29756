{% extends "base.html" %}

{% block title %}Login - Dr. Resume{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6 col-lg-4">
        <div class="card shadow">
            <div class="card-header bg-primary text-white text-center">
                <h4>🧩 Welcome Back</h4>
                <p class="mb-0">Sign in to your Dr. Resume account</p>
            </div>
            <div class="card-body">
                <!-- Login Form -->
                <form id="loginForm">
                    <div class="mb-3">
                        <label for="email" class="form-label">Email Address</label>
                        <input type="email" class="form-control" id="email" name="email" required>
                        <div class="invalid-feedback" id="emailError"></div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="password" class="form-label">Password</label>
                        <input type="password" class="form-control" id="password" name="password" required>
                        <div class="invalid-feedback" id="passwordError"></div>
                    </div>
                    
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="rememberMe">
                        <label class="form-check-label" for="rememberMe">
                            Remember me
                        </label>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary" id="loginBtn">
                            <span class="spinner-border spinner-border-sm d-none" id="loadingSpinner"></span>
                            Sign In
                        </button>
                    </div>
                </form>
                
                <!-- Success/Error Messages -->
                <div id="alertContainer" class="mt-3"></div>
            </div>
            <div class="card-footer text-center">
                <small>Don't have an account? <a href="/register">Register here</a></small>
            </div>
        </div>
    </div>
</div>

<!-- Quick Demo Section -->
<div class="row mt-5">
    <div class="col-12">
        <div class="alert alert-info text-center">
            <h5>🚀 Quick Demo</h5>
            <p class="mb-2">Want to try Dr. Resume without creating an account?</p>
            <button class="btn btn-outline-info btn-sm" onclick="fillDemoCredentials()">
                Use Demo Account
            </button>
        </div>
    </div>
</div>

<!-- Features Reminder -->
<div class="row mt-4">
    <div class="col-12">
        <h5 class="text-center mb-4">What you can do with Dr. Resume:</h5>
    </div>
    <div class="col-md-3 text-center mb-3">
        <div class="text-primary mb-2">📄</div>
        <small>Upload Resume</small>
    </div>
    <div class="col-md-3 text-center mb-3">
        <div class="text-primary mb-2">🎯</div>
        <small>Match Job Descriptions</small>
    </div>
    <div class="col-md-3 text-center mb-3">
        <div class="text-primary mb-2">📊</div>
        <small>Get Compatibility Score</small>
    </div>
    <div class="col-md-3 text-center mb-3">
        <div class="text-warning mb-2">⭐</div>
        <small>AI Suggestions (Premium)</small>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.getElementById('loginForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const email = document.getElementById('email').value;
    const password = document.getElementById('password').value;
    
    // Clear previous errors
    clearErrors();
    
    // Basic validation
    if (!email || !password) {
        showAlert('danger', 'Please fill in all fields');
        return;
    }
    
    // Show loading state
    showLoading(true);
    
    try {
        const response = await fetch('/api/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                email: email,
                password: password
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            // Store JWT token in localStorage
            localStorage.setItem('dr_resume_token', data.token);
            localStorage.setItem('dr_resume_user', JSON.stringify(data.user));
            
            showAlert('success', 'Login successful! Redirecting to dashboard...');
            
            // Redirect to dashboard after short delay
            setTimeout(() => {
                window.location.href = '/dashboard';
            }, 1500);
        } else {
            showAlert('danger', data.message);
        }
    } catch (error) {
        showAlert('danger', 'Network error. Please try again.');
    } finally {
        showLoading(false);
    }
});

function clearErrors() {
    document.querySelectorAll('.is-invalid').forEach(el => el.classList.remove('is-invalid'));
    document.querySelectorAll('.invalid-feedback').forEach(el => el.textContent = '');
}

function showAlert(type, message) {
    const alertContainer = document.getElementById('alertContainer');
    alertContainer.innerHTML = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
}

function showLoading(isLoading) {
    const btn = document.getElementById('loginBtn');
    const spinner = document.getElementById('loadingSpinner');
    
    if (isLoading) {
        btn.disabled = true;
        spinner.classList.remove('d-none');
        btn.innerHTML = '<span class="spinner-border spinner-border-sm"></span> Signing In...';
    } else {
        btn.disabled = false;
        spinner.classList.add('d-none');
        btn.innerHTML = 'Sign In';
    }
}

function fillDemoCredentials() {
    document.getElementById('email').value = '<EMAIL>';
    document.getElementById('password').value = 'DemoPassword123';
    showAlert('info', 'Demo credentials filled. Click "Sign In" to continue.');
}

// Check if user is already logged in
document.addEventListener('DOMContentLoaded', function() {
    const token = localStorage.getItem('dr_resume_token');
    if (token) {
        // User is already logged in, redirect to dashboard
        window.location.href = '/dashboard';
    }
});
</script>
{% endblock %}
