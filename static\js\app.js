/**
 * Dr. Resume - Main JavaScript Application
 * Common functions and utilities used across the application
 */

// Global app object
const DrResume = {
    // API base URL
    apiUrl: '/api',
    
    // Common utility functions
    utils: {
        /**
         * Show loading state on button
         * @param {string} buttonId - ID of the button element
         * @param {boolean} isLoading - Whether to show loading state
         * @param {string} loadingText - Text to show while loading
         */
        toggleButtonLoading: function(buttonId, isLoading, loadingText = 'Loading...') {
            const button = document.getElementById(buttonId);
            if (!button) return;
            
            if (isLoading) {
                button.disabled = true;
                button.dataset.originalText = button.innerHTML;
                button.innerHTML = `<span class="spinner-border spinner-border-sm me-2"></span>${loadingText}`;
            } else {
                button.disabled = false;
                button.innerHTML = button.dataset.originalText || button.innerHTML;
            }
        },
        
        /**
         * Show alert message
         * @param {string} containerId - ID of the container element
         * @param {string} type - Alert type (success, danger, warning, info)
         * @param {string} message - Message to display
         */
        showAlert: function(containerId, type, message) {
            const container = document.getElementById(containerId);
            if (!container) return;
            
            container.innerHTML = `
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
        },
        
        /**
         * Clear form validation errors
         * @param {string} formId - ID of the form element
         */
        clearFormErrors: function(formId) {
            const form = document.getElementById(formId);
            if (!form) return;
            
            form.querySelectorAll('.is-invalid').forEach(el => {
                el.classList.remove('is-invalid');
            });
            form.querySelectorAll('.invalid-feedback').forEach(el => {
                el.textContent = '';
            });
        },
        
        /**
         * Show form field error
         * @param {string} fieldId - ID of the field element
         * @param {string} message - Error message to display
         */
        showFieldError: function(fieldId, message) {
            const field = document.getElementById(fieldId);
            if (!field) return;
            
            field.classList.add('is-invalid');
            const feedback = field.parentNode.querySelector('.invalid-feedback');
            if (feedback) {
                feedback.textContent = message;
            }
        },
        
        /**
         * Validate email format
         * @param {string} email - Email to validate
         * @returns {boolean} - Whether email is valid
         */
        isValidEmail: function(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        },
        
        /**
         * Format file size for display
         * @param {number} bytes - File size in bytes
         * @returns {string} - Formatted file size
         */
        formatFileSize: function(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        },
        
        /**
         * Get JWT token from localStorage
         * @returns {string|null} - JWT token or null if not found
         */
        getToken: function() {
            return localStorage.getItem('dr_resume_token');
        },
        
        /**
         * Set JWT token in localStorage
         * @param {string} token - JWT token to store
         */
        setToken: function(token) {
            localStorage.setItem('dr_resume_token', token);
        },
        
        /**
         * Remove JWT token from localStorage
         */
        removeToken: function() {
            localStorage.removeItem('dr_resume_token');
        },
        
        /**
         * Check if user is authenticated
         * @returns {boolean} - Whether user has valid token
         */
        isAuthenticated: function() {
            const token = this.getToken();
            if (!token) return false;
            
            try {
                // Basic JWT validation (check if it has 3 parts)
                const parts = token.split('.');
                return parts.length === 3;
            } catch (e) {
                return false;
            }
        }
    },
    
    // API helper functions
    api: {
        /**
         * Make authenticated API request
         * @param {string} endpoint - API endpoint
         * @param {object} options - Fetch options
         * @returns {Promise} - Fetch promise
         */
        request: async function(endpoint, options = {}) {
            const token = DrResume.utils.getToken();
            const defaultOptions = {
                headers: {
                    'Content-Type': 'application/json',
                    ...(token && { 'Authorization': `Bearer ${token}` })
                }
            };
            
            const finalOptions = {
                ...defaultOptions,
                ...options,
                headers: {
                    ...defaultOptions.headers,
                    ...options.headers
                }
            };
            
            const response = await fetch(`${DrResume.apiUrl}${endpoint}`, finalOptions);
            
            // Handle unauthorized responses
            if (response.status === 401) {
                DrResume.utils.removeToken();
                window.location.href = '/login';
                return;
            }
            
            return response;
        }
    }
};

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Add fade-in animation to main content
    const mainContent = document.querySelector('main');
    if (mainContent) {
        mainContent.classList.add('fade-in');
    }
    
    // Auto-dismiss alerts after 5 seconds
    setTimeout(() => {
        document.querySelectorAll('.alert').forEach(alert => {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        });
    }, 5000);
});

// Export for use in other scripts
window.DrResume = DrResume;
