# Dr. Resume - Environment Configuration Template
# Copy this file to .env and replace with your actual values

# Flask Configuration
SECRET_KEY=your-secret-key-here
JWT_SECRET_KEY=your-jwt-secret-key-here

# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/dr_resume

# Redis Configuration (for caching)
REDIS_URL=redis://localhost:6379/0

# File Upload Configuration
UPLOAD_FOLDER=uploads/resumes
MAX_CONTENT_LENGTH=16777216

# OpenAI API Configuration (for Premium Features)
OPENAI_API_KEY=your-openai-api-key-here

# Email Configuration (for future features)
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=True
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-email-password

# Development/Production Mode
FLASK_ENV=development
DEBUG=True
