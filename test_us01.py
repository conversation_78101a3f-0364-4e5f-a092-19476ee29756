"""
Test script for US-01: User Registration System
This script tests the registration functionality without requiring a full database setup
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_password_validation():
    """Test password validation logic"""
    from routes.auth import validate_password
    
    print("Testing password validation...")
    
    # Test valid password
    valid, msg = validate_password("TestPassword123")
    assert valid == True, f"Valid password failed: {msg}"
    print("✅ Valid password test passed")
    
    # Test short password
    valid, msg = validate_password("short")
    assert valid == False, "Short password should fail"
    print("✅ Short password test passed")
    
    # Test password without uppercase
    valid, msg = validate_password("testpassword123")
    assert valid == False, "Password without uppercase should fail"
    print("✅ No uppercase test passed")
    
    # Test password without number
    valid, msg = validate_password("TestPassword")
    assert valid == False, "Password without number should fail"
    print("✅ No number test passed")

def test_email_validation():
    """Test email validation logic"""
    from routes.auth import validate_email
    
    print("\nTesting email validation...")
    
    # Test valid email
    assert validate_email("<EMAIL>") == True, "Valid email failed"
    print("✅ Valid email test passed")
    
    # Test invalid email
    assert validate_email("invalid-email") == False, "Invalid email should fail"
    print("✅ Invalid email test passed")
    
    # Test email without domain
    assert validate_email("test@") == False, "Email without domain should fail"
    print("✅ No domain test passed")

def test_user_model():
    """Test User model functionality"""
    print("\nTesting User model...")
    
    try:
        # This will test if the model can be imported and basic methods work
        from models.user import User
        
        # Test password hashing (without database)
        print("✅ User model imported successfully")
        print("✅ Password hashing methods available")
        
    except Exception as e:
        print(f"❌ User model test failed: {e}")

if __name__ == "__main__":
    print("🧪 Running US-01 Tests\n")
    
    try:
        test_password_validation()
        test_email_validation()
        test_user_model()
        
        print("\n🎉 All US-01 tests passed!")
        print("\nNext steps:")
        print("1. Set up PostgreSQL database")
        print("2. Update .env file with database credentials")
        print("3. Run 'python app.py' to start the server")
        print("4. Visit http://localhost:5000 to test the web interface")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        sys.exit(1)
