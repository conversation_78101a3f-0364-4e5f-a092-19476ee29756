# US-02: Login and JWT Authentication System

## 🎯 What We Built
A complete login system with JWT (JSON Web Token) authentication, secure session management, and a responsive dashboard. This builds upon US-01's registration system to provide full user authentication.

## 📋 Features Implemented
- **Login API**: `POST /api/login` endpoint with credential verification
- **JWT Tokens**: Secure token generation and validation
- **Current User API**: `GET /api/me` endpoint for user information
- **Logout API**: `POST /api/logout` endpoint
- **Login Page**: Responsive form with validation
- **Dashboard**: Protected user dashboard
- **Navigation**: Dynamic navigation based on authentication state
- **Token Management**: localStorage integration for session persistence

## 🔧 API Endpoints

### POST /api/login
Authenticate user and return JWT token.

**Request Body:**
```json
{
    "email": "<EMAIL>",
    "password": "SecurePassword123"
}
```

**Success Response (200):**
```json
{
    "success": true,
    "message": "Login successful",
    "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "user": {
        "id": 1,
        "email": "<EMAIL>",
        "is_premium": false
    }
}
```

**Error Responses:**
- **400**: Missing email or password
- **401**: Invalid credentials or deactivated account
- **500**: Internal server error

### GET /api/me
Get current authenticated user information.

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Success Response (200):**
```json
{
    "success": true,
    "user": {
        "id": 1,
        "email": "<EMAIL>",
        "is_premium": false,
        "is_active": true,
        "created_at": "2024-01-01T00:00:00",
        "updated_at": "2024-01-01T00:00:00"
    }
}
```

### POST /api/logout
Logout current user (client-side token removal).

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Success Response (200):**
```json
{
    "success": true,
    "message": "Logout successful"
}
```

## 🌐 Web Routes

### GET /login
Login form page with email/password fields.

### GET /dashboard
Protected dashboard page (requires authentication).

## 🔒 JWT Security Features

### Token Structure
- **Header**: Algorithm and token type
- **Payload**: User ID and additional claims (email, premium status)
- **Signature**: HMAC SHA256 signature for verification

### Token Claims
```json
{
    "sub": "1",  // User ID
    "email": "<EMAIL>",
    "is_premium": false,
    "iat": 1640995200,  // Issued at
    "exp": 1641081600   // Expires at
}
```

### Security Measures
- **Secret Key**: JWT signed with secret key from environment
- **Expiration**: Tokens have configurable expiration time
- **Validation**: All protected routes verify token signature
- **User Verification**: Token user ID verified against database

## 📁 Files Created/Modified

### Backend Files
- **`routes/auth.py`**: Added login, logout, and current user endpoints
- **`models/user.py`**: Enhanced with authentication methods (from US-01)

### Frontend Files
- **`templates/login.html`**: Login form with validation and demo credentials
- **`templates/dashboard.html`**: Protected dashboard with user info and quick actions
- **`templates/base.html`**: Updated navigation with authentication state management
- **`static/js/app.js`**: Enhanced with JWT token utilities

## 🎓 Learning Guide for Beginners

### What is JWT Authentication?
JWT (JSON Web Token) is a secure way to transmit information between parties:
- **Stateless**: Server doesn't need to store session data
- **Secure**: Cryptographically signed to prevent tampering
- **Portable**: Can be used across different services
- **Self-contained**: Contains all necessary user information

### How JWT Works
1. **User logs in** → Server verifies credentials
2. **Server creates JWT** → Signs token with secret key
3. **Client stores token** → Usually in localStorage or cookies
4. **Client sends token** → In Authorization header for API requests
5. **Server verifies token** → Checks signature and expiration

### Authentication Flow
```
Login Form → API Request → Credential Check → JWT Creation → 
Token Storage → Protected Requests → Token Verification → Access Granted
```

### Why Use JWT?
- **Scalability**: No server-side session storage needed
- **Security**: Tamper-proof with cryptographic signatures
- **Flexibility**: Can include custom claims (user role, permissions)
- **Standards**: Industry-standard authentication method

## 🔄 Execution Flow

### Login Process
1. **User visits `/login`** → Flask serves login form
2. **User enters credentials** → JavaScript validates input
3. **Form submission** → POST request to `/api/login`
4. **Server verification** → Check email/password against database
5. **JWT generation** → Create signed token with user claims
6. **Token response** → Send token and user info to client
7. **Token storage** → JavaScript stores token in localStorage
8. **Dashboard redirect** → User redirected to protected dashboard

### Protected Route Access
1. **User visits `/dashboard`** → JavaScript checks for stored token
2. **Token verification** → GET request to `/api/me` with token
3. **Server validation** → Verify token signature and user existence
4. **Access granted** → Dashboard content loaded with user data

### Logout Process
1. **User clicks logout** → JavaScript calls logout function
2. **API call** → POST request to `/api/logout` (optional)
3. **Token removal** → Remove token from localStorage
4. **Redirect** → User redirected to home page

## 🚀 How to Test

### 1. Start the Application
```bash
# Ensure US-01 setup is complete
python app.py
```

### 2. Test Registration and Login Flow
1. Visit `http://localhost:5000/register`
2. Create a new account
3. Visit `http://localhost:5000/login`
4. Login with your credentials
5. Verify redirect to dashboard

### 3. Test API Endpoints
```bash
# Test login
curl -X POST http://localhost:5000/api/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"TestPassword123"}'

# Test current user (replace TOKEN with actual JWT)
curl -H "Authorization: Bearer TOKEN" \
  http://localhost:5000/api/me

# Test logout
curl -X POST http://localhost:5000/api/logout \
  -H "Authorization: Bearer TOKEN"
```

### 4. Test Authentication States
1. **Logged out**: Navigation shows Register/Login
2. **Logged in**: Navigation shows Dashboard/Logout
3. **Protected routes**: Dashboard requires authentication
4. **Token persistence**: Refresh page maintains login state

## 🐛 Common Issues and Solutions

### JWT Token Errors
- **Problem**: "Token has expired"
- **Solution**: Implement token refresh or extend expiration time

### Authentication Redirect Loop
- **Problem**: Constant redirects between login and dashboard
- **Solution**: Check token validation logic and localStorage access

### CORS Issues
- **Problem**: API calls blocked by browser
- **Solution**: Ensure Flask-CORS is properly configured

### Token Not Persisting
- **Problem**: User logged out on page refresh
- **Solution**: Verify localStorage is working and token is being stored

## 📝 What's Next?
This authentication system provides the foundation for:
- **US-03**: Resume upload with user association
- **US-04**: Job description storage per user
- **US-05+**: All user-specific features and data

The JWT authentication will be used to:
- Protect all API endpoints
- Associate uploaded files with users
- Manage premium feature access
- Track user activity and history
