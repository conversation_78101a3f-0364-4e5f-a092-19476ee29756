# US-01: User Registration System

## 🎯 What We Built
A complete user registration system with secure password hashing, email validation, and a responsive frontend form. This includes both API endpoints and web interface.

## 📋 Features Implemented
- **Backend API**: `POST /api/register` endpoint
- **Frontend UI**: Registration form with validation
- **Security**: Password hashing with bcrypt
- **Validation**: Email format and password strength validation
- **Database**: PostgreSQL integration with SQLAlchemy
- **Error Handling**: Comprehensive error responses

## 🗃️ Database Schema

### Users Table
```sql
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    email VARCHAR(120) UNIQUE NOT NULL,
    password_hash VARCHAR(128) NOT NULL,
    is_premium BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 📁 Files Created/Modified

### Backend Files
- **`models/user.py`**: User model with password hashing and validation methods
- **`routes/auth.py`**: Authentication routes including registration API and web pages
- **`app.py`**: Updated to import models and register blueprints

### Frontend Files
- **`templates/base.html`**: Base template with Bootstrap styling and navigation
- **`templates/home.html`**: Landing page with features and call-to-action
- **`templates/register.html`**: Registration form with client-side validation
- **`static/css/style.css`**: Custom CSS styling for the application
- **`static/js/app.js`**: JavaScript utilities and API helpers

## 🔧 API Endpoints

### POST /api/register
Register a new user account.

**Request Body:**
```json
{
    "email": "<EMAIL>",
    "password": "SecurePassword123"
}
```

**Success Response (201):**
```json
{
    "success": true,
    "message": "User registered successfully",
    "user": {
        "id": 1,
        "email": "<EMAIL>",
        "is_premium": false
    }
}
```

**Error Responses:**
- **400**: Invalid input data
- **409**: Email already exists
- **500**: Internal server error

### GET /api/health
Health check endpoint to verify API status.

**Response (200):**
```json
{
    "success": true,
    "message": "Dr. Resume API is running",
    "version": "1.0.0"
}
```

## 🌐 Web Routes

### GET /
Home page with application overview and features.

### GET /register
Registration form page.

## 🔒 Security Features

### Password Requirements
- Minimum 8 characters
- At least one uppercase letter
- At least one lowercase letter
- At least one number

### Password Hashing
- Uses bcrypt with salt for secure password storage
- Passwords are never stored in plain text

### Email Validation
- Format validation using regex
- Duplicate email prevention

## 🚀 How to Test

### 1. Start the Application
```bash
# Activate virtual environment
venv\Scripts\activate  # Windows
source venv/bin/activate  # macOS/Linux

# Install dependencies
pip install -r requirements.txt

# Set up environment variables
cp .env.example .env
# Edit .env with your database credentials

# Run the application
python app.py
```

### 2. Test the Web Interface
1. Open browser to `http://localhost:5000`
2. Click "Get Started Free" or navigate to `/register`
3. Fill out the registration form
4. Submit and verify success message

### 3. Test the API Directly
```bash
# Test health endpoint
curl http://localhost:5000/api/health

# Test registration endpoint
curl -X POST http://localhost:5000/api/register \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"TestPassword123"}'
```

## 🎓 Learning Guide for Beginners

### What is User Registration?
User registration is the process of creating new user accounts in a system. It typically involves:
1. Collecting user information (email, password)
2. Validating the information
3. Storing it securely in a database
4. Providing feedback to the user

### Why Hash Passwords?
- **Security**: Plain text passwords can be easily stolen
- **Bcrypt**: Adds salt and uses slow hashing to prevent rainbow table attacks
- **One-way**: Hashed passwords cannot be reversed to get original password

### Database Models with SQLAlchemy
SQLAlchemy is an ORM (Object-Relational Mapping) that lets you work with databases using Python objects:
- **Model**: Python class that represents a database table
- **Columns**: Class attributes that represent table columns
- **Methods**: Functions that operate on the data

### Flask Blueprints
Blueprints help organize routes into logical groups:
- **auth_bp**: Web routes for serving HTML pages
- **api_bp**: API routes for JSON responses
- **Separation**: Keeps different types of routes organized

## 🔄 Execution Flow

### Registration Process
1. **User visits `/register`** → Flask serves registration form
2. **User fills form** → JavaScript validates input client-side
3. **Form submission** → JavaScript sends POST to `/api/register`
4. **Server validation** → Flask validates email/password format
5. **Database check** → Check if email already exists
6. **Password hashing** → Bcrypt hashes the password
7. **Database save** → SQLAlchemy saves user to PostgreSQL
8. **Response** → JSON response sent back to frontend
9. **UI update** → JavaScript shows success/error message

### Code Flow
```
User Input → JavaScript Validation → API Request → Flask Route → 
Model Validation → Database Save → JSON Response → UI Update
```

## 🐛 Common Issues and Solutions

### Database Connection Error
- **Problem**: `psycopg2.OperationalError`
- **Solution**: Check PostgreSQL is running and credentials in `.env`

### Import Errors
- **Problem**: `ModuleNotFoundError`
- **Solution**: Ensure virtual environment is activated and dependencies installed

### Password Validation Fails
- **Problem**: Password doesn't meet requirements
- **Solution**: Check password has uppercase, lowercase, number, and 8+ characters

## 📝 What's Next?
This registration system provides the foundation for:
- **US-02**: Login system with JWT tokens
- **US-03**: File upload with user authentication
- **US-04**: User-specific data storage

The user model will be referenced by all future features that require user authentication and data association.
