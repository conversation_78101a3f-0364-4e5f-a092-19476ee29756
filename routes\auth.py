"""
Dr. <PERSON>sume - Authentication Routes
API endpoints for user registration and authentication
"""

from flask import Blueprint, request, jsonify, render_template
from flask_jwt_extended import create_access_token, jwt_required, get_jwt_identity, get_jwt
import re
from app import db
from models.user import User

# Create authentication blueprint
auth_bp = Blueprint('auth', __name__)

# Create API blueprint for API endpoints
api_bp = Blueprint('api', __name__, url_prefix='/api')

def validate_email(email):
    """
    Validate email format using regex
    
    Args:
        email (str): Email address to validate
        
    Returns:
        bool: True if valid email format, False otherwise
    """
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def validate_password(password):
    """
    Validate password strength
    
    Args:
        password (str): Password to validate
        
    Returns:
        tuple: (is_valid, error_message)
    """
    if len(password) < 8:
        return False, "Password must be at least 8 characters long"
    
    if not re.search(r'[A-Z]', password):
        return False, "Password must contain at least one uppercase letter"
    
    if not re.search(r'[a-z]', password):
        return False, "Password must contain at least one lowercase letter"
    
    if not re.search(r'\d', password):
        return False, "Password must contain at least one number"
    
    return True, ""

@api_bp.route('/register', methods=['POST'])
def register():
    """
    User Registration Endpoint
    
    POST /api/register
    
    Expected JSON payload:
    {
        "email": "<EMAIL>",
        "password": "SecurePassword123"
    }
    
    Returns:
        JSON response with success/failure status
    """
    try:
        # Get JSON data from request
        data = request.get_json()
        
        # Validate request data
        if not data:
            return jsonify({
                'success': False,
                'message': 'No data provided'
            }), 400
        
        email = data.get('email', '').strip()
        password = data.get('password', '')
        
        # Validate required fields
        if not email or not password:
            return jsonify({
                'success': False,
                'message': 'Email and password are required'
            }), 400
        
        # Validate email format
        if not validate_email(email):
            return jsonify({
                'success': False,
                'message': 'Invalid email format'
            }), 400
        
        # Validate password strength
        is_valid_password, password_error = validate_password(password)
        if not is_valid_password:
            return jsonify({
                'success': False,
                'message': password_error
            }), 400
        
        # Check if email already exists
        if User.email_exists(email):
            return jsonify({
                'success': False,
                'message': 'Email already registered'
            }), 409
        
        # Create new user
        new_user = User(email=email, password=password)
        
        # Save to database
        db.session.add(new_user)
        db.session.commit()
        
        # Return success response
        return jsonify({
            'success': True,
            'message': 'User registered successfully',
            'user': {
                'id': new_user.id,
                'email': new_user.email,
                'is_premium': new_user.is_premium
            }
        }), 201
        
    except Exception as e:
        # Rollback database changes on error
        db.session.rollback()
        
        # Log error (in production, use proper logging)
        print(f"Registration error: {str(e)}")
        
        return jsonify({
            'success': False,
            'message': 'Internal server error'
        }), 500

@api_bp.route('/login', methods=['POST'])
def login():
    """
    User Login Endpoint

    POST /api/login

    Expected JSON payload:
    {
        "email": "<EMAIL>",
        "password": "SecurePassword123"
    }

    Returns:
        JSON response with JWT token on success
    """
    try:
        # Get JSON data from request
        data = request.get_json()

        # Validate request data
        if not data:
            return jsonify({
                'success': False,
                'message': 'No data provided'
            }), 400

        email = data.get('email', '').strip()
        password = data.get('password', '')

        # Validate required fields
        if not email or not password:
            return jsonify({
                'success': False,
                'message': 'Email and password are required'
            }), 400

        # Find user by email
        user = User.find_by_email(email)
        if not user:
            return jsonify({
                'success': False,
                'message': 'Invalid email or password'
            }), 401

        # Check if user account is active
        if not user.is_active:
            return jsonify({
                'success': False,
                'message': 'Account is deactivated'
            }), 401

        # Verify password
        if not user.check_password(password):
            return jsonify({
                'success': False,
                'message': 'Invalid email or password'
            }), 401

        # Create JWT token
        token = create_access_token(
            identity=user.id,
            additional_claims={
                'email': user.email,
                'is_premium': user.is_premium
            }
        )

        # Return success response with token
        return jsonify({
            'success': True,
            'message': 'Login successful',
            'token': token,
            'user': {
                'id': user.id,
                'email': user.email,
                'is_premium': user.is_premium
            }
        }), 200

    except Exception as e:
        # Log error (in production, use proper logging)
        print(f"Login error: {str(e)}")

        return jsonify({
            'success': False,
            'message': 'Internal server error'
        }), 500

@api_bp.route('/me', methods=['GET'])
@jwt_required()
def get_current_user():
    """
    Get Current User Information

    GET /api/me

    Headers:
        Authorization: Bearer <jwt_token>

    Returns:
        JSON response with current user information
    """
    try:
        # Get user ID from JWT token
        user_id = get_jwt_identity()

        # Find user in database
        user = User.query.get(user_id)
        if not user:
            return jsonify({
                'success': False,
                'message': 'User not found'
            }), 404

        # Check if user account is active
        if not user.is_active:
            return jsonify({
                'success': False,
                'message': 'Account is deactivated'
            }), 401

        # Return user information
        return jsonify({
            'success': True,
            'user': user.to_dict()
        }), 200

    except Exception as e:
        print(f"Get current user error: {str(e)}")
        return jsonify({
            'success': False,
            'message': 'Internal server error'
        }), 500

@api_bp.route('/logout', methods=['POST'])
@jwt_required()
def logout():
    """
    User Logout Endpoint

    POST /api/logout

    Headers:
        Authorization: Bearer <jwt_token>

    Returns:
        JSON response confirming logout
    """
    try:
        # In a more advanced implementation, you would add the token to a blacklist
        # For now, we'll just return a success message
        # The frontend will remove the token from localStorage

        return jsonify({
            'success': True,
            'message': 'Logout successful'
        }), 200

    except Exception as e:
        print(f"Logout error: {str(e)}")
        return jsonify({
            'success': False,
            'message': 'Internal server error'
        }), 500

@api_bp.route('/health', methods=['GET'])
def health_check():
    """
    Health check endpoint to verify API is working

    GET /api/health

    Returns:
        JSON response with API status
    """
    return jsonify({
        'success': True,
        'message': 'Dr. Resume API is running',
        'version': '1.0.0'
    }), 200

# Web routes for serving HTML pages
@auth_bp.route('/', methods=['GET'])
def home():
    """
    Serve the home page

    GET /

    Returns:
        HTML home page
    """
    return render_template('home.html')

@auth_bp.route('/register', methods=['GET'])
def register_page():
    """
    Serve the registration page

    GET /register

    Returns:
        HTML page for user registration
    """
    return render_template('register.html')

@auth_bp.route('/login', methods=['GET'])
def login_page():
    """
    Serve the login page

    GET /login

    Returns:
        HTML page for user login
    """
    return render_template('login.html')

@auth_bp.route('/dashboard', methods=['GET'])
def dashboard_page():
    """
    Serve the dashboard page

    GET /dashboard

    Returns:
        HTML dashboard page (requires authentication)
    """
    return render_template('dashboard.html')
