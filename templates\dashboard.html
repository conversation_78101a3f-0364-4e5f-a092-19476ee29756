{% extends "base.html" %}

{% block title %}Dashboard - Dr. Resume{% endblock %}

{% block content %}
<!-- User Welcome Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2>Welcome back, <span id="userName">User</span>! 👋</h2>
                <p class="text-muted">Ready to optimize your resume?</p>
            </div>
            <div>
                <button class="btn btn-outline-danger" onclick="logout()">
                    Logout
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Quick Stats -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-primary" id="totalScans">0</h3>
                <p class="mb-0">Total Scans</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-success" id="avgScore">0%</h3>
                <p class="mb-0">Average Score</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-warning" id="premiumStatus">Free</h3>
                <p class="mb-0">Account Type</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-info" id="lastScan">Never</h3>
                <p class="mb-0">Last Scan</p>
            </div>
        </div>
    </div>
</div>

<!-- Main Actions -->
<div class="row mb-4">
    <div class="col-12">
        <h4>Quick Actions</h4>
    </div>
    <div class="col-md-6 mb-3">
        <div class="card h-100">
            <div class="card-body text-center">
                <div class="display-4 text-primary mb-3">📄</div>
                <h5>Upload New Resume</h5>
                <p class="text-muted">Start a new analysis by uploading your resume</p>
                <button class="btn btn-primary" onclick="showComingSoon('Resume Upload')">
                    Upload Resume
                </button>
            </div>
        </div>
    </div>
    <div class="col-md-6 mb-3">
        <div class="card h-100">
            <div class="card-body text-center">
                <div class="display-4 text-success mb-3">🎯</div>
                <h5>Analyze Job Match</h5>
                <p class="text-muted">Compare your resume with a job description</p>
                <button class="btn btn-success" onclick="showComingSoon('Job Analysis')">
                    Analyze Match
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<div class="row mb-4">
    <div class="col-12">
        <h4>Recent Activity</h4>
        <div class="card">
            <div class="card-body">
                <div id="recentActivity" class="text-center text-muted py-4">
                    <p>No recent activity. Start by uploading your resume!</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Premium Section -->
<div class="row">
    <div class="col-12">
        <div class="card border-warning" id="premiumCard">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0">⭐ Upgrade to Premium</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <h6>Unlock Advanced AI Features</h6>
                        <ul class="mb-3">
                            <li>AI-powered resume suggestions</li>
                            <li>Advanced keyword optimization</li>
                            <li>Industry-specific recommendations</li>
                            <li>Unlimited scans and analysis</li>
                        </ul>
                    </div>
                    <div class="col-md-4 text-center">
                        <button class="btn btn-warning btn-lg" onclick="showComingSoon('Premium Upgrade')">
                            Upgrade Now
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Coming Soon Modal -->
<div class="modal fade" id="comingSoonModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">🚧 Coming Soon</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p id="comingSoonMessage">This feature is coming soon!</p>
                <p class="text-muted">We're building this feature as part of the next user stories (US-03 to US-10).</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Check authentication on page load
document.addEventListener('DOMContentLoaded', async function() {
    const token = localStorage.getItem('dr_resume_token');
    
    if (!token) {
        // No token, redirect to login
        window.location.href = '/login';
        return;
    }
    
    try {
        // Verify token and get user info
        const response = await fetch('/api/me', {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });
        
        if (response.ok) {
            const data = await response.json();
            if (data.success) {
                updateUserInfo(data.user);
            } else {
                throw new Error('Invalid token');
            }
        } else {
            throw new Error('Authentication failed');
        }
    } catch (error) {
        // Token is invalid, remove it and redirect to login
        localStorage.removeItem('dr_resume_token');
        localStorage.removeItem('dr_resume_user');
        window.location.href = '/login';
    }
});

function updateUserInfo(user) {
    // Update user name
    const userName = document.getElementById('userName');
    if (userName) {
        userName.textContent = user.email.split('@')[0];
    }
    
    // Update premium status
    const premiumStatus = document.getElementById('premiumStatus');
    if (premiumStatus) {
        premiumStatus.textContent = user.is_premium ? 'Premium' : 'Free';
        premiumStatus.className = user.is_premium ? 'text-warning' : 'text-info';
    }
    
    // Hide premium card if user is already premium
    if (user.is_premium) {
        const premiumCard = document.getElementById('premiumCard');
        if (premiumCard) {
            premiumCard.style.display = 'none';
        }
    }
}

async function logout() {
    const token = localStorage.getItem('dr_resume_token');
    
    try {
        // Call logout API
        await fetch('/api/logout', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });
    } catch (error) {
        console.log('Logout API call failed, but continuing with local logout');
    }
    
    // Remove token from localStorage
    localStorage.removeItem('dr_resume_token');
    localStorage.removeItem('dr_resume_user');
    
    // Redirect to home page
    window.location.href = '/';
}

function showComingSoon(feature) {
    const modal = new bootstrap.Modal(document.getElementById('comingSoonModal'));
    const message = document.getElementById('comingSoonMessage');
    message.textContent = `${feature} feature is coming soon!`;
    modal.show();
}
</script>
{% endblock %}
