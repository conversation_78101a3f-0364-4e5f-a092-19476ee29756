# Dr. Resume - Project Setup Guide

## 🎯 What We Built
This is the initial project structure for the Dr. Resume AI Resume Scanner application. We've created the foundation that will support all 10 user stories (US-01 to US-10).

## 📁 Project Structure Created
```
dr-resume/
├── app.py                 # Main Flask application entry point
├── config.py             # Configuration management for different environments
├── requirements.txt      # Python dependencies
├── .env.example         # Environment variables template
├── models/              # Database models (SQLAlchemy)
│   └── __init__.py
├── routes/              # API route handlers (Flask blueprints)
│   └── __init__.py
├── utils/               # Utility functions (file processing, NLP)
│   └── __init__.py
├── templates/           # HTML templates (Jinja2)
├── static/              # Static files (CSS, JS, images)
│   ├── css/
│   └── js/
├── uploads/             # Local file storage
│   └── resumes/
└── tests/               # Unit and integration tests
```

## 🔧 Required Files Explanation

### Core Application Files
- **app.py**: Main Flask application with configuration, database initialization, and server startup
- **config.py**: Centralized configuration management for development, production, and testing environments
- **requirements.txt**: All Python dependencies needed for the project

### Configuration Files
- **.env.example**: Template for environment variables (copy to .env and fill with actual values)

### Directory Structure
- **models/**: Database models using SQLAlchemy ORM
- **routes/**: API endpoints organized as Flask blueprints
- **utils/**: Helper functions for file processing, NLP, and common tasks
- **templates/**: HTML templates using Jinja2 templating engine
- **static/**: CSS, JavaScript, and image files
- **uploads/**: Local storage for uploaded resume files
- **tests/**: Unit and integration tests

## 🚀 Setup Instructions

### 1. Prerequisites
- Python 3.9 or higher
- PostgreSQL database
- Redis server (optional, for caching)

### 2. Environment Setup
```bash
# Create virtual environment
python -m venv venv

# Activate virtual environment
# On Windows:
venv\Scripts\activate
# On macOS/Linux:
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt
```

### 3. Database Setup
```bash
# Install PostgreSQL and create database
createdb dr_resume

# Update .env file with your database credentials
cp .env.example .env
# Edit .env with your actual values
```

### 4. Configuration
Copy `.env.example` to `.env` and update with your actual values:
- Database connection string
- Secret keys for Flask and JWT
- OpenAI API key (for premium features)
- Upload folder path

### 5. Run Application
```bash
python app.py
```

## 🎓 Learning Guide for Beginners

### What is Flask?
Flask is a lightweight Python web framework that makes it easy to build web applications. It handles:
- HTTP requests and responses
- URL routing
- Template rendering
- Session management

### What is SQLAlchemy?
SQLAlchemy is an Object-Relational Mapping (ORM) tool that lets you work with databases using Python objects instead of writing SQL queries directly.

### What is JWT?
JSON Web Tokens (JWT) are a secure way to transmit information between parties. We use them for user authentication.

### Project Flow
1. **app.py** starts the Flask server
2. **config.py** loads environment-specific settings
3. **models/** defines database structure
4. **routes/** handles API requests
5. **utils/** provides helper functions
6. **templates/** renders HTML pages

## 🔄 Execution Flow
1. User makes HTTP request to Flask application
2. Flask routes the request to appropriate handler in routes/
3. Route handler may use models/ to interact with database
4. Route handler may use utils/ for processing
5. Response is sent back to user (JSON for API, HTML for web pages)

## 📝 What's Next?
This setup provides the foundation for building all 10 user stories:
- US-01: User Registration (will add User model and auth routes)
- US-02: Login & JWT (will add login routes and token management)
- US-03: Resume Upload (will add file handling and storage)
- And so on...

Each subsequent US will build upon this foundation, adding specific models, routes, and utilities as needed.

## 🔒 Security Notes
- Never commit .env file to version control
- Use strong, unique secret keys in production
- Keep API keys secure and rotate them regularly
- Use HTTPS in production
