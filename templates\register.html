{% extends "base.html" %}

{% block title %}Register - Dr. Resume{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6 col-lg-4">
        <div class="card shadow">
            <div class="card-header bg-primary text-white text-center">
                <h4>🧩 Create Account</h4>
                <p class="mb-0">Join Dr. Resume to start optimizing your resume</p>
            </div>
            <div class="card-body">
                <!-- Registration Form -->
                <form id="registerForm">
                    <div class="mb-3">
                        <label for="email" class="form-label">Email Address</label>
                        <input type="email" class="form-control" id="email" name="email" required>
                        <div class="invalid-feedback" id="emailError"></div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="password" class="form-label">Password</label>
                        <input type="password" class="form-control" id="password" name="password" required>
                        <div class="form-text">
                            Password must be at least 8 characters with uppercase, lowercase, and number.
                        </div>
                        <div class="invalid-feedback" id="passwordError"></div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="confirmPassword" class="form-label">Confirm Password</label>
                        <input type="password" class="form-control" id="confirmPassword" name="confirmPassword" required>
                        <div class="invalid-feedback" id="confirmPasswordError"></div>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary" id="registerBtn">
                            <span class="spinner-border spinner-border-sm d-none" id="loadingSpinner"></span>
                            Register
                        </button>
                    </div>
                </form>
                
                <!-- Success/Error Messages -->
                <div id="alertContainer" class="mt-3"></div>
            </div>
            <div class="card-footer text-center">
                <small>Already have an account? <a href="/login">Login here</a></small>
            </div>
        </div>
    </div>
</div>

<!-- Features Section -->
<div class="row mt-5">
    <div class="col-12">
        <h3 class="text-center mb-4">Why Choose Dr. Resume?</h3>
    </div>
    <div class="col-md-4 text-center">
        <div class="card h-100">
            <div class="card-body">
                <h5>📊 AI-Powered Analysis</h5>
                <p>Get detailed insights on how well your resume matches job descriptions.</p>
            </div>
        </div>
    </div>
    <div class="col-md-4 text-center">
        <div class="card h-100">
            <div class="card-body">
                <h5>🎯 Keyword Optimization</h5>
                <p>Discover missing keywords and improve your ATS compatibility.</p>
            </div>
        </div>
    </div>
    <div class="col-md-4 text-center">
        <div class="card h-100">
            <div class="card-body">
                <h5>⭐ Premium Suggestions</h5>
                <p>Get AI-powered recommendations to enhance your resume content.</p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.getElementById('registerForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const email = document.getElementById('email').value;
    const password = document.getElementById('password').value;
    const confirmPassword = document.getElementById('confirmPassword').value;
    
    // Clear previous errors
    clearErrors();
    
    // Validate passwords match
    if (password !== confirmPassword) {
        showError('confirmPasswordError', 'Passwords do not match');
        return;
    }
    
    // Show loading state
    showLoading(true);
    
    try {
        const response = await fetch('/api/register', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                email: email,
                password: password
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            showAlert('success', 'Registration successful! You can now login.');
            document.getElementById('registerForm').reset();
        } else {
            showAlert('danger', data.message);
        }
    } catch (error) {
        showAlert('danger', 'Network error. Please try again.');
    } finally {
        showLoading(false);
    }
});

function clearErrors() {
    document.querySelectorAll('.is-invalid').forEach(el => el.classList.remove('is-invalid'));
    document.querySelectorAll('.invalid-feedback').forEach(el => el.textContent = '');
}

function showError(elementId, message) {
    const errorElement = document.getElementById(elementId);
    const inputElement = errorElement.previousElementSibling;
    
    errorElement.textContent = message;
    inputElement.classList.add('is-invalid');
}

function showAlert(type, message) {
    const alertContainer = document.getElementById('alertContainer');
    alertContainer.innerHTML = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
}

function showLoading(isLoading) {
    const btn = document.getElementById('registerBtn');
    const spinner = document.getElementById('loadingSpinner');
    
    if (isLoading) {
        btn.disabled = true;
        spinner.classList.remove('d-none');
        btn.innerHTML = '<span class="spinner-border spinner-border-sm"></span> Registering...';
    } else {
        btn.disabled = false;
        spinner.classList.add('d-none');
        btn.innerHTML = 'Register';
    }
}
</script>
{% endblock %}
