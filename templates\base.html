<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Dr. Resume - AI Resume Scanner{% endblock %}</title>
    
    <!-- Bootstrap CSS for quick styling -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    
    {% block head %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <strong>🧩 Dr. Resume</strong>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">Home</a>
                    </li>
                    <!-- Show when not authenticated -->
                    <li class="nav-item" id="nav-register">
                        <a class="nav-link" href="/register">Register</a>
                    </li>
                    <li class="nav-item" id="nav-login">
                        <a class="nav-link" href="/login">Login</a>
                    </li>
                    <!-- Show when authenticated -->
                    <li class="nav-item d-none" id="nav-dashboard">
                        <a class="nav-link" href="/dashboard">Dashboard</a>
                    </li>
                    <li class="nav-item d-none" id="nav-logout">
                        <a class="nav-link" href="#" onclick="logoutFromNav()">Logout</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container mt-4">
        <!-- Flash Messages -->
        <div id="flash-messages"></div>
        
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="bg-light mt-5 py-4">
        <div class="container text-center">
            <p>&copy; 2024 Dr. Resume - AI Resume Scanner. Built for learning purposes.</p>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/app.js') }}"></script>

    <!-- Authentication state management -->
    <script>
    // Update navigation based on authentication state
    document.addEventListener('DOMContentLoaded', function() {
        updateNavigation();
    });

    function updateNavigation() {
        const token = localStorage.getItem('dr_resume_token');
        const isAuthenticated = token !== null;

        // Elements to show/hide based on auth state
        const authElements = ['nav-register', 'nav-login'];
        const unauthElements = ['nav-dashboard', 'nav-logout'];

        if (isAuthenticated) {
            // Hide auth elements, show unauth elements
            authElements.forEach(id => {
                const el = document.getElementById(id);
                if (el) el.classList.add('d-none');
            });
            unauthElements.forEach(id => {
                const el = document.getElementById(id);
                if (el) el.classList.remove('d-none');
            });
        } else {
            // Show auth elements, hide unauth elements
            authElements.forEach(id => {
                const el = document.getElementById(id);
                if (el) el.classList.remove('d-none');
            });
            unauthElements.forEach(id => {
                const el = document.getElementById(id);
                if (el) el.classList.add('d-none');
            });
        }
    }

    function logoutFromNav() {
        localStorage.removeItem('dr_resume_token');
        localStorage.removeItem('dr_resume_user');
        window.location.href = '/';
    }
    </script>

    {% block scripts %}{% endblock %}
</body>
</html>
