"""
Dr. Resume - User Model
Database model for user registration and authentication
"""

from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
import bcrypt

# Import db from app.py (will be properly imported in __init__.py)
from app import db

class User(db.Model):
    """
    User model for storing user registration and authentication data
    
    Attributes:
        id: Primary key, auto-incrementing integer
        email: User's email address (unique, required)
        password_hash: Hashed password using bcrypt
        is_premium: Boolean flag for premium subscription status
        created_at: Timestamp when user was created
        updated_at: Timestamp when user was last updated
    """
    
    __tablename__ = 'users'
    
    # Primary key
    id = db.Column(db.Integer, primary_key=True)
    
    # User credentials
    email = db.Column(db.String(120), unique=True, nullable=False, index=True)
    password_hash = db.Column(db.String(128), nullable=False)
    
    # User status
    is_premium = db.Column(db.<PERSON>, default=False, nullable=False)
    is_active = db.Column(db.<PERSON>, default=True, nullable=False)
    
    # Timestamps
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    def __init__(self, email, password):
        """
        Initialize a new user with email and password
        
        Args:
            email (str): User's email address
            password (str): Plain text password (will be hashed)
        """
        self.email = email.lower().strip()  # Normalize email
        self.set_password(password)
    
    def set_password(self, password):
        """
        Hash and set the user's password
        
        Args:
            password (str): Plain text password to hash
        """
        # Generate salt and hash password
        salt = bcrypt.gensalt()
        self.password_hash = bcrypt.hashpw(password.encode('utf-8'), salt).decode('utf-8')
    
    def check_password(self, password):
        """
        Check if provided password matches the stored hash
        
        Args:
            password (str): Plain text password to verify
            
        Returns:
            bool: True if password matches, False otherwise
        """
        return bcrypt.checkpw(password.encode('utf-8'), self.password_hash.encode('utf-8'))
    
    def to_dict(self):
        """
        Convert user object to dictionary for JSON serialization
        
        Returns:
            dict: User data (excluding sensitive information)
        """
        return {
            'id': self.id,
            'email': self.email,
            'is_premium': self.is_premium,
            'is_active': self.is_active,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }
    
    def __repr__(self):
        """String representation of User object"""
        return f'<User {self.email}>'
    
    @staticmethod
    def find_by_email(email):
        """
        Find user by email address
        
        Args:
            email (str): Email address to search for
            
        Returns:
            User: User object if found, None otherwise
        """
        return User.query.filter_by(email=email.lower().strip()).first()
    
    @staticmethod
    def email_exists(email):
        """
        Check if email already exists in database
        
        Args:
            email (str): Email address to check
            
        Returns:
            bool: True if email exists, False otherwise
        """
        return User.query.filter_by(email=email.lower().strip()).first() is not None
